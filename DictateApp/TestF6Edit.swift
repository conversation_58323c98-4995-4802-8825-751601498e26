#!/usr/bin/env swift

import Foundation

print("🧪 Testing F6 Edit API Call...")

// Test the exact API call that's failing
func testOpenAIAPI() async {
    let apiKey = "********************************************************************************************************************************************************************"
    
    let url = URL(string: "https://api.openai.com/v1/chat/completions")!
    var request = URLRequest(url: url)
    request.httpMethod = "POST"
    request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
    request.setValue("application/json", forHTTPHeaderField: "Content-Type")
    
    let prompt = """
    You are a writing assistant that edits text with contextual awareness.
    Given previous statements from the current session and an edit instruction, modify the text by:
    - applying the requested changes
    - using context to resolve any ambiguities in the instruction
    - preserving the user's original tone and intent
    - ensuring the edited text flows naturally with the previous context
    Return only the edited text, no explanations.
    
    Previous context from this session:
    Previous statements (chronological order):
    - "Test one, two, three."
    
    Apply the edit instruction to this text: "Test one, two, three."
    Based on this instruction: "Remove three."
    
    Return only the modified text:
    """
    
    let requestBody: [String: Any] = [
        "model": "gpt-4o",
        "messages": [
            [
                "role": "user",
                "content": prompt
            ]
        ],
        "max_tokens": 500,
        "temperature": 0.3
    ]
    
    do {
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        print("🤖 Sending request to OpenAI API...")
        print("🤖 URL: \(url)")
        print("🤖 API Key: \(apiKey.prefix(20))...")
        print("🤖 Prompt length: \(prompt.count) characters")
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        if let httpResponse = response as? HTTPURLResponse {
            print("🤖 HTTP Status: \(httpResponse.statusCode)")
            
            if httpResponse.statusCode == 200 {
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    print("✅ API call succeeded!")
                    print("✅ Response: \(json)")
                    
                    if let choices = json["choices"] as? [[String: Any]],
                       let firstChoice = choices.first,
                       let message = firstChoice["message"] as? [String: Any],
                       let content = message["content"] as? String {
                        print("✅ Edited text: '\(content.trimmingCharacters(in: .whitespacesAndNewlines))'")
                    }
                } else {
                    print("❌ Failed to parse JSON response")
                }
            } else {
                print("❌ HTTP Error: \(httpResponse.statusCode)")
                if let errorData = String(data: data, encoding: .utf8) {
                    print("❌ Error response: \(errorData)")
                }
            }
        }
        
    } catch {
        print("❌ Request failed: \(error)")
        print("❌ Error type: \(type(of: error))")
        print("❌ Error description: \(error.localizedDescription)")
    }
}

// Run the test
await testOpenAIAPI()
print("🧪 Test completed")
