import Foundation
import AVFoundation
import Combine

protocol AudioCaptureRepository {
    func startRecording(mode: RecordingMode) async throws
    func stopRecording() async
    var audioFilePublisher: AnyPublisher<URL, Never> { get }
    var isRecording: Bool { get }
}

class AVFoundationAudioCaptureRepository: NSObject, AudioCaptureRepository, ObservableObject {
    private let audioEngine = AVAudioEngine()
    private let audioFileSubject = PassthroughSubject<URL, Never>()
    private var recordingTimer: Timer?
    private var silenceTimer: Timer?
    private var currentRecordingURL: URL?
    private var audioFile: AVAudioFile?
    
    @Published private(set) var isRecording = false
    
    private let sampleRate: Double = 44100
    private let channels: UInt32 = 1
    private let bitDepth: UInt32 = 16
    
    var audioFilePublisher: AnyPublisher<URL, Never> {
        audioFileSubject.eraseToAnyPublisher()
    }

    override init() {
        super.init()
        setupAudioSession()
    }
    
    private func setupAudioSession() {
        // macOS doesn't use AVAudioSession like iOS
        // Audio session setup is handled automatically by AVAudioEngine
    }
    
    func startRecording(mode: RecordingMode) async throws {
        guard !isRecording else { return }

        try await requestMicrophonePermission()

        let tempURL = createTemporaryAudioURL()
        currentRecordingURL = tempURL

        let inputNode = audioEngine.inputNode
        let inputFormat = inputNode.outputFormat(forBus: 0)

        print("🎤 Input format: \(inputFormat)")
        print("🎤 Sample rate: \(inputFormat.sampleRate), Channels: \(inputFormat.channelCount)")

        // Use the input node's native format for the tap to avoid format mismatch
        let tapFormat = inputFormat

        // Create audio file for recording with compatible settings
        let fileSettings: [String: Any] = [
            AVFormatIDKey: kAudioFormatLinearPCM,
            AVSampleRateKey: tapFormat.sampleRate,
            AVNumberOfChannelsKey: tapFormat.channelCount,
            AVLinearPCMBitDepthKey: 16,
            AVLinearPCMIsFloatKey: false,
            AVLinearPCMIsBigEndianKey: false
        ]

        audioFile = try AVAudioFile(forWriting: tempURL, settings: fileSettings)

        print("🎤 Installing tap with format: \(tapFormat)")

        // Install tap for recording using the input node's native format
        inputNode.installTap(onBus: 0, bufferSize: 4096, format: tapFormat) { [weak self] buffer, _ in
            guard let self = self, let audioFile = self.audioFile else { return }

            // Write to file
            do {
                try audioFile.write(from: buffer)
            } catch {
                print("Failed to write audio buffer: \(error)")
            }
        }

        try audioEngine.start()
        isRecording = true

        print("🎤 Audio recording started successfully")
    }
    
    func stopRecording() async {
        guard isRecording else { return }

        print("🎤 Stopping audio recording...")

        recordingTimer?.invalidate()
        recordingTimer = nil

        silenceTimer?.invalidate()
        silenceTimer = nil

        // Safely remove tap and stop engine
        do {
            audioEngine.inputNode.removeTap(onBus: 0)
            audioEngine.stop()
            print("🎤 Audio engine stopped successfully")
        } catch {
            print("⚠️ Error stopping audio engine: \(error)")
        }

        audioFile = nil
        isRecording = false

        if let url = currentRecordingURL {
            print("🎤 Sending audio file for processing: \(url.lastPathComponent)")
            audioFileSubject.send(url)
            currentRecordingURL = nil
        }
    }
    
    private func requestMicrophonePermission() async throws {
        // On macOS, we use AVCaptureDevice for microphone permissions
        let status = AVCaptureDevice.authorizationStatus(for: .audio)
        
        switch status {
        case .authorized:
            return
        case .denied, .restricted:
            throw PermissionError.microphoneNotAuthorized
        case .notDetermined:
            let granted = await AVCaptureDevice.requestAccess(for: .audio)
            if !granted {
                throw PermissionError.microphoneNotAuthorized
            }
        @unknown default:
            throw PermissionError.systemError("Unknown microphone permission status")
        }
    }
    
    private func createTemporaryAudioURL() -> URL {
        let tempDir = FileManager.default.temporaryDirectory
        let filename = "recording_\(Date().timeIntervalSince1970).wav"
        return tempDir.appendingPathComponent(filename)
    }
}

