import SwiftUI

struct PromptLibraryView: View {
    @EnvironmentObject var appState: AppState
    @Environment(\.dismiss) private var dismiss
    @State private var showingAddPrompt = false
    @State private var editingPrompt: PromptTemplate?
    @State private var searchText = ""
    
    var filteredPrompts: [PromptTemplate] {
        if searchText.isEmpty {
            return appState.promptLibrary.prompts
        } else {
            return appState.promptLibrary.prompts.filter { prompt in
                prompt.name.localizedCaseInsensitiveContains(searchText) ||
                prompt.description.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with title and buttons
            HStack {
                Text("Prompt Library")
                    .font(.title2)
                    .fontWeight(.semibold)

                Spacer()

                But<PERSON>(action: { showingAddPrompt = true }) {
                    HStack(spacing: 4) {
                        Image(systemName: "plus")
                        Text("New Prompt")
                    }
                }
                .buttonStyle(.borderedProminent)
                .help("Add new prompt")

                Button("Done") {
                    dismiss()
                }
                .buttonStyle(.bordered)
            }
            .padding()
            .background(Color(NSColor.controlBackgroundColor))

            // Search bar
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                TextField("Search prompts...", text: $searchText)
                    .textFieldStyle(.roundedBorder)
            }
            .padding(.horizontal)
            .padding(.bottom)

                List {
                    // Built-in prompts section
                    if !appState.promptLibrary.builtInPrompts.isEmpty {
                        Section("Built-in Prompts") {
                            ForEach(filteredPrompts.filter { $0.isBuiltIn }) { prompt in
                                PromptRowView(
                                    prompt: prompt,
                                    isSelected: appState.settings.selectedPromptId == prompt.id,
                                    onSelect: { selectPrompt(prompt) },
                                    onEdit: nil // Built-in prompts can't be edited
                                )
                            }
                        }
                    }
                    
                    // User prompts section
                    let userPrompts = filteredPrompts.filter { !$0.isBuiltIn }
                    if !userPrompts.isEmpty {
                        Section("My Prompts") {
                            ForEach(userPrompts) { prompt in
                                PromptRowView(
                                    prompt: prompt,
                                    isSelected: appState.settings.selectedPromptId == prompt.id,
                                    onSelect: { selectPrompt(prompt) },
                                    onEdit: { editingPrompt = prompt }
                                )
                            }
                            .onDelete(perform: deleteUserPrompts)
                        }
                    }
                    
                    // Empty state
                    if filteredPrompts.isEmpty {
                        Section {
                            VStack(spacing: 12) {
                                Image(systemName: "doc.text.magnifyingglass")
                                    .font(.largeTitle)
                                    .foregroundColor(.secondary)
                                
                                Text(searchText.isEmpty ? "No prompts available" : "No prompts match your search")
                                    .font(.headline)
                                    .foregroundColor(.secondary)
                                
                                if searchText.isEmpty {
                                    Text("Create your first custom prompt to get started")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .multilineTextAlignment(.center)
                                }
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 40)
                        }
                    }
            }
            .listStyle(.sidebar)
        }
        .frame(minWidth: 600, minHeight: 500)
        .frame(idealWidth: 800, idealHeight: 600)
        .sheet(isPresented: $showingAddPrompt) {
            PromptEditorView(prompt: nil) { newPrompt in
                appState.promptLibrary.addPrompt(newPrompt)
            }
        }
        .sheet(item: $editingPrompt) { prompt in
            PromptEditorView(prompt: prompt) { updatedPrompt in
                appState.promptLibrary.updatePrompt(updatedPrompt)
            }
        }
    }
    
    private func selectPrompt(_ prompt: PromptTemplate) {
        var newSettings = appState.settings
        newSettings.selectedPromptId = prompt.id
        appState.updateSettings(newSettings)
    }
    
    private func deleteUserPrompts(at offsets: IndexSet) {
        let userPrompts = filteredPrompts.filter { !$0.isBuiltIn }
        for index in offsets {
            let prompt = userPrompts[index]
            appState.promptLibrary.deletePrompt(prompt)
            
            // If the deleted prompt was selected, clear the selection
            if appState.settings.selectedPromptId == prompt.id {
                var newSettings = appState.settings
                newSettings.selectedPromptId = nil
                appState.updateSettings(newSettings)
            }
        }
    }
}

struct PromptRowView: View {
    let prompt: PromptTemplate
    let isSelected: Bool
    let onSelect: () -> Void
    let onEdit: (() -> Void)?

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    Text(prompt.name)
                        .font(.headline)
                        .foregroundColor(isSelected ? .accentColor : .primary)
                        .lineLimit(1)

                    if prompt.isBuiltIn {
                        Text("Built-in")
                            .font(.caption)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.secondary.opacity(0.2))
                            .cornerRadius(4)
                    }

                    Spacer()

                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.accentColor)
                    }
                }

                Text(prompt.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                    .fixedSize(horizontal: false, vertical: true)

                Text(prompt.systemPrompt)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(4)
                    .fixedSize(horizontal: false, vertical: true)
                    .padding(.top, 2)
            }

            VStack(spacing: 8) {
                if let onEdit = onEdit {
                    Button(action: onEdit) {
                        Image(systemName: "pencil")
                            .foregroundColor(.accentColor)
                            .font(.title3)
                    }
                    .buttonStyle(.plain)
                    .help("Edit prompt")
                }
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            onSelect()
        }
        .contextMenu {
            Button(action: onSelect) {
                Label(isSelected ? "Selected" : "Select", systemImage: isSelected ? "checkmark.circle.fill" : "circle")
            }

            if let onEdit = onEdit {
                Button(action: onEdit) {
                    Label("Edit", systemImage: "pencil")
                }
            }
        }
    }
}

#Preview {
    PromptLibraryView()
        .environmentObject({
            let appState = AppState()
            return appState
        }())
}
