import Foundation

struct AppSettings: Codable {
    var openAIAPIKey: String = ""
    var openAIModel: String = "gpt-4o"
    var whisperModel: WhisperModel = .largev3Turbo
    var memoryBufferSize: Int = 10
    var insertionMethod: InsertionMethod = .clipboard
    var editMaxTokens: Int = 500
    var editTemperature: Float = 0.3
    var f3Enabled: Bool = true
    var f3MemoryCount: Int = 5
    var contextualEnabled: Bool = true
    var contextualMemoryCount: Int = 5

    static let `default` = AppSettings()
}

enum WhisperModel: String, Codable, CaseIterable {
    case largev3Turbo = "large-v3-turbo"
    case largev3 = "large-v3"
    case medium = "medium"
    case small = "small"
    case base = "base"
    case tiny = "tiny"
    
    var displayName: String {
        switch self {
        case .largev3Turbo: return "Large v3 Turbo (Recommended)"
        case .largev3: return "Large v3 (Highest Accuracy)"
        case .medium: return "Medium (Balanced)"
        case .small: return "Small (Fast)"
        case .base: return "Base (Fastest)"
        case .tiny: return "Tiny (Development)"
        }
    }
}

enum InsertionMethod: String, Codable, CaseIterable {
    case clipboard = "clipboard"
    case accessibility = "accessibility"
    
    var displayName: String {
        switch self {
        case .clipboard: return "Copy to Clipboard"
        case .accessibility: return "Insert Directly (Requires Permission)"
        }
    }
}