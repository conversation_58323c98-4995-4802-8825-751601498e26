import Foundation

// Test individual functions directly
print("🧪 TESTING BUTTON AND HOTKEY FUNCTIONS")

// Mock to track if startRecording was called
class MockViewModel {
    var startRecordingCalled = false
    var startRecordingMode: String = ""
    
    func startRecording(mode: String) {
        startRecordingCalled = true
        startRecordingMode = mode
        print("✅ MockViewModel.startRecording called with mode: \(mode)")
    }
    
    func stopRecording() {
        print("✅ MockViewModel.stopRecording called")
    }
}

// Test 1: Button Action Function
print("\n📱 TESTING BUTTON ACTION...")
let mockViewModel = MockViewModel()

// Simulate button action logic (from RecordingModeButton.buttonAction)
func simulateButtonAction(canUseMode: Bool, isActive: Bool, mode: String) {
    print("🔘 Button clicked for mode: \(mode), canUse: \(canUseMode), isActive: \(isActive)")
    
    if !canUseMode {
        print("❌ Button disabled - cannot use mode")
        return
    }
    
    if isActive {
        mockViewModel.stopRecording()
    } else {
        mockViewModel.startRecording(mode: mode)
    }
}

// Test button with good conditions
simulateButtonAction(canUseMode: true, isActive: false, mode: "voiceActivity")
print("✅ Button test 1 - startRecording called: \(mockViewModel.startRecordingCalled)")

// Reset and test disabled button
mockViewModel.startRecordingCalled = false
simulateButtonAction(canUseMode: false, isActive: false, mode: "voiceActivity")
print("✅ Button test 2 - startRecording called: \(mockViewModel.startRecordingCalled)")

// Test 2: Hotkey Event Function
print("\n🔥 TESTING HOTKEY EVENT...")

// Simulate hotkey event logic (from MainViewModel.handleHotkeyEvent)
func simulateHotkeyEvent(event: String) {
    print("🔥 Processing hotkey event: \(event)")

    switch event {
    case "toggleRecording":
        // For test, assume not recording
        mockViewModel.startRecording(mode: "toggle")
    default:
        print("❌ Unknown hotkey event")
    }
}

// Reset and test hotkey
mockViewModel.startRecordingCalled = false
simulateHotkeyEvent(event: "toggleRecording")
print("✅ Hotkey test - startRecording called: \(mockViewModel.startRecordingCalled)")

print("\n🧪 TESTS COMPLETE")
print("If these work but the app doesn't, the issue is in the wiring between UI and these functions")