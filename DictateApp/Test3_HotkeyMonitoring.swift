#!/usr/bin/env swift

import Foundation
import AppKit

print("🧪 TEST 3: Hotkey Monitoring")

class TestHotkeyMonitor {
    private var monitor: Any?
    private var eventCount = 0
    
    func startMonitoring() {
        print("1. Attempting to start NSEvent global monitor...")
        
        monitor = NSEvent.addGlobalMonitorForEvents(matching: .keyDown) { event in
            self.eventCount += 1
            print("🔥 Global key event detected! KeyCode: \(event.keyCode), Count: \(self.eventCount)")
            
            // Test specific keys
            switch event.keyCode {
            case 103: // F7
                print("   → F7 detected!")
            case 100: // F8
                print("   → F8 detected!")
            case 36: // Enter
                print("   → Enter detected!")
            default:
                print("   → Other key: \(event.keyCode)")
            }
        }
        
        if monitor != nil {
            print("✅ Global monitor created successfully")
            print("   Press F8, F3, or Enter keys to test...")
            print("   (Will test for 5 seconds)")
            
            // Test for 5 seconds
            RunLoop.current.run(until: Date().addingTimeInterval(5))
            
            print("2. Test complete. Events detected: \(eventCount)")
            
            if eventCount > 0 {
                print("✅ RESULT: Hotkey monitoring works")
            } else {
                print("❌ RESULT: No events detected - hotkey monitoring failed")
                print("❌ This explains why hotkeys don't work in the app")
            }
        } else {
            print("❌ RESULT: Failed to create global monitor")
            print("❌ This explains why hotkeys don't work in the app")
        }
    }
    
    func stopMonitoring() {
        if let monitor = monitor {
            NSEvent.removeMonitor(monitor)
            self.monitor = nil
            print("Monitor stopped")
        }
    }
}

let tester = TestHotkeyMonitor()
tester.startMonitoring()
tester.stopMonitoring()