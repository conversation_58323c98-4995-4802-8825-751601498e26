import XCTest
@testable import DictateApp

class LLMRepositoryTests: XCTestCase {
    
    func testEditTextAPICall() async throws {
        // Test the exact F6 edit scenario that's failing
        print("🧪 Testing LLM editText API call...")
        
        // Use the actual API key
        let apiKey = "********************************************************************************************************************************************************************"
        let llmRepository = OpenAILLMRepository(apiKey: apiKey)
        
        let originalText = "Test one, two, three."
        let editInstruction = "Remove three."
        let context = """
        Previous statements (chronological order):
        - "Test one, two, three."
        """
        
        print("🧪 Original text: '\(originalText)'")
        print("🧪 Edit instruction: '\(editInstruction)'")
        print("🧪 Context: '\(context)'")
        
        do {
            let result = try await llmRepository.editText(originalText, instruction: editInstruction, context: context)
            print("✅ LLM API call succeeded!")
            print("✅ Result: '\(result)'")
            
            // Basic validation
            XCTAssertFalse(result.isEmpty, "Result should not be empty")
            XCTAssertNotEqual(result, originalText, "Result should be different from original")
            
        } catch {
            print("❌ LLM API call failed: \(error)")
            print("❌ Error type: \(type(of: error))")
            print("❌ Error description: \(error.localizedDescription)")
            
            // Print more details about the error
            if let llmError = error as? LLMError {
                print("❌ LLM Error: \(llmError)")
            }
            
            // Fail the test with detailed error info
            XCTFail("LLM API call failed: \(error)")
        }
    }
    
    func testEditTextWithEmptyAPIKey() async throws {
        print("🧪 Testing LLM with empty API key...")
        
        let llmRepository = OpenAILLMRepository(apiKey: "")
        
        do {
            let _ = try await llmRepository.editText("test", instruction: "edit", context: "")
            XCTFail("Should have thrown noAPIKey error")
        } catch LLMError.noAPIKey {
            print("✅ Correctly threw noAPIKey error")
        } catch {
            XCTFail("Unexpected error: \(error)")
        }
    }
    
    func testEditTextWithInvalidAPIKey() async throws {
        print("🧪 Testing LLM with invalid API key...")
        
        let llmRepository = OpenAILLMRepository(apiKey: "invalid-key")
        
        do {
            let _ = try await llmRepository.editText("test", instruction: "edit", context: "")
            XCTFail("Should have thrown API error")
        } catch LLMError.apiError(let message) {
            print("✅ Correctly threw API error: \(message)")
        } catch {
            print("❌ Unexpected error type: \(error)")
            XCTFail("Unexpected error: \(error)")
        }
    }
}
