import SwiftUI

struct ContentView: View {
    @EnvironmentObject private var appState: AppState
    @StateObject private var viewModel: MainViewModel
    
    init(appState: AppState) {
        let audioRepo = AVFoundationAudioCaptureRepository()
        let transcriptionRepo = WhisperCppTranscriptionRepository()
        let apiKey = Self.loadOpenAIKey()
        let llmRepo = OpenAILLMRepository(apiKey: apiKey)
        let textOutputRepo = SystemTextOutputRepository()
        let permissionsRepo = SystemPermissionsRepository()
        let hotkeyRepo = NSEventHotkeyRepository()
        let keyboardRepo = CGEventKeyboardRepository()
        
        _viewModel = StateObject(wrappedValue: MainViewModel(
            appState: appState,
            audioRepository: audioRepo,
            transcriptionRepository: transcriptionRepo,
            llmRepository: llmRepo,
            textOutputRepository: textOutputRepo,
            permissionsRepository: permissionsRepo,
            hotkeyRepository: hotkeyRepo,
            keyboardRepository: keyboardRepo
        ))
    }
    
    var body: some View {
        NavigationSplitView {
            // Sidebar
            VStack(alignment: .leading, spacing: 16) {
                // Status section
                StatusSection()
                
                Divider()
                
                // Recording modes
                RecordingModesSection(viewModel: viewModel)
                
                Divider()
                
                // Actions
                ActionsSection(viewModel: viewModel)
                
                Spacer()
                
                // Settings button
                Button("Settings") {
                    print("🔧 Settings button clicked!")
                    print("🔧 Setting appState.isSettingsPresented = true")
                    appState.isSettingsPresented = true
                    print("🔧 appState.isSettingsPresented is now: \(appState.isSettingsPresented)")
                }
                .buttonStyle(.bordered)
            }
            .padding()
            .frame(minWidth: 250, maxWidth: 300)
        } detail: {
            // Main content area
            VStack {
                if appState.transcriptionHistory.isEmpty {
                    EmptyStateView()
                } else {
                    TranscriptionHistoryView()
                }
            }
            .frame(minWidth: 600, minHeight: 400)
        }
        .sheet(isPresented: $appState.isSettingsPresented) {
            SettingsView()
                .environmentObject(appState)
        }
        .alert("Error", isPresented: $viewModel.showError) {
            Button("OK") { }
        } message: {
            Text(viewModel.errorMessage)
        }
        .onAppear {
            appState.loadSettings()
        }
    }
    
    private static func loadOpenAIKey() -> String {
        print("🔑 Loading OpenAI API key...")

        // First check environment variable
        if let envKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"], !envKey.isEmpty {
            print("🔑 Found API key in environment variable")
            return envKey
        }

        // Then check AppSettings (new method)
        if let data = UserDefaults.standard.data(forKey: "AppSettings"),
           let savedSettings = try? JSONDecoder().decode(AppSettings.self, from: data),
           !savedSettings.openAIAPIKey.isEmpty {
            print("🔑 Found API key in AppSettings: \(savedSettings.openAIAPIKey.prefix(10))...")
            return savedSettings.openAIAPIKey
        }

        // Then check legacy UserDefaults key
        if let savedKey = UserDefaults.standard.string(forKey: "OpenAI_API_Key"), !savedKey.isEmpty {
            print("🔑 Found API key in legacy UserDefaults")
            return savedKey
        }

        // If no key found, show a warning
        print("⚠️ No OpenAI API key found. Set OPENAI_API_KEY environment variable or configure in settings.")
        print("⚠️ AI features (contextual transcription, editing) will not work without an API key.")
        return ""
    }
}

struct StatusSection: View {
    @EnvironmentObject private var appState: AppState
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Status")
                .font(.headline)
            
            HStack {
                Circle()
                    .fill(statusColor)
                    .frame(width: 12, height: 12)
                
                Text(appState.systemStatus.displayText)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // Permissions status
            if !appState.permissions.allGranted {
                VStack(alignment: .leading, spacing: 4) {
                    if !appState.permissions.microphone {
                        Label("Microphone access required", systemImage: "mic.slash")
                            .foregroundColor(.orange)
                            .font(.caption)
                    }
                    
                    if !appState.permissions.accessibility {
                        Label("Accessibility access recommended", systemImage: "accessibility")
                            .foregroundColor(.orange)
                            .font(.caption)
                    }
                }
            }
        }
    }
    
    private var statusColor: Color {
        switch appState.systemStatus {
        case .idle:
            return .green
        case .listening:
            return .blue
        case .processing:
            return .orange
        case .error:
            return .red
        }
    }
}

struct RecordingModesSection: View {
    @EnvironmentObject private var appState: AppState
    let viewModel: MainViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recording Modes")
                .font(.headline)
            
            VStack(spacing: 8) {
                RecordingModeButton(
                    mode: .toggle,
                    viewModel: viewModel,
                    keyEquivalent: "F8"
                )

                RecordingModeButton(
                    mode: .contextual,
                    viewModel: viewModel,
                    keyEquivalent: "F3"
                )

                RecordingModeButton(
                    mode: .edit,
                    viewModel: viewModel,
                    keyEquivalent: "F6",
                    requiresHistory: true
                )

                RecordingModeButton(
                    mode: .contextualEdit,
                    viewModel: viewModel,
                    keyEquivalent: "F4",
                    requiresHistory: true
                )
            }
        }
    }
}

struct RecordingModeButton: View {
    @EnvironmentObject private var appState: AppState
    let mode: RecordingMode
    let viewModel: MainViewModel
    let keyEquivalent: String?
    let requiresHistory: Bool
    
    init(mode: RecordingMode, viewModel: MainViewModel, keyEquivalent: String? = nil, requiresHistory: Bool = false) {
        self.mode = mode
        self.viewModel = viewModel
        self.keyEquivalent = keyEquivalent
        self.requiresHistory = requiresHistory
    }
    
    var body: some View {
        Button(action: buttonAction) {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(mode.displayName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Spacer()
                    
                    if let keyEquivalent = keyEquivalent {
                        Text(keyEquivalent)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.secondary.opacity(0.2))
                            .cornerRadius(4)
                    }
                }
                
                Text(mode.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .buttonStyle(RecordingButtonStyle(isActive: isActive))
        .disabled(!canUseMode)
    }
    
    private var isActive: Bool {
        if case .listening(let currentMode) = appState.systemStatus {
            return currentMode == mode
        }
        return false
    }
    
    private var canUseMode: Bool {
        let hasPermissions = viewModel.canRecord
        let hasHistory = !requiresHistory || !appState.transcriptionHistory.isEmpty
        let result = hasPermissions && hasHistory
        print("🔍 canUseMode: \(result) (hasPermissions: \(hasPermissions), hasHistory: \(hasHistory), requiresHistory: \(requiresHistory))")
        return result
    }
    
    private func buttonAction() {
        print("🚨🚨🚨 BUTTON CLICKED! BUTTON CLICKED! BUTTON CLICKED! 🚨🚨🚨")
        print("🔘 Mode: \(mode.displayName)")
        print("🔘 canUse: \(canUseMode)")
        print("🔘 isActive: \(isActive)")
        
        if !canUseMode {
            print("❌ BUTTON DISABLED - ACTION BLOCKED")
            return
        }
        
        if isActive {
            print("🛑 STOPPING RECORDING...")
            viewModel.stopRecording()
        } else {
            print("🎤 STARTING RECORDING...")
            viewModel.startRecording(mode: mode)
        }
        print("✅ BUTTON ACTION COMPLETE!")
    }
}

struct RecordingButtonStyle: ButtonStyle {
    let isActive: Bool
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(backgroundColor(configuration: configuration))
                    .stroke(borderColor, lineWidth: isActive ? 2 : 1)
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
    
    private func backgroundColor(configuration: Configuration) -> Color {
        if isActive {
            return Color.blue.opacity(0.2)
        } else if configuration.isPressed {
            return Color.primary.opacity(0.1)
        } else {
            return Color.clear
        }
    }
    
    private var borderColor: Color {
        isActive ? .blue : .secondary.opacity(0.3)
    }
}

struct ActionsSection: View {
    let viewModel: MainViewModel
    @EnvironmentObject private var appState: AppState
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Actions")
                .font(.headline)
            
            Button("Clear History") {
                viewModel.clearHistory()
            }
            .buttonStyle(.bordered)
            .disabled(appState.transcriptionHistory.isEmpty)
        }
    }
}

struct EmptyStateView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "mic.circle")
                .font(.system(size: 80))
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                Text("Ready to Transcribe")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Choose a recording mode from the sidebar to begin")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text("Tip: Use keyboard shortcuts for quick access")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text("F8 - Toggle Recording • F3 - Contextual • F6 - Edit")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(40)
    }
}


#Preview {
    let appState = AppState()
    return ContentView(appState: appState)
        .environmentObject(appState)
}