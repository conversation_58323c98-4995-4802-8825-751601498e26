import SwiftUI

struct PromptEditorView: View {
    let originalPrompt: PromptTemplate?
    let onSave: (PromptTemplate) -> Void
    
    @Environment(\.dismiss) private var dismiss
    @State private var name: String = ""
    @State private var description: String = ""
    @State private var systemPrompt: String = ""
    @State private var showingDiscardAlert = false
    
    private var isEditing: Bool {
        originalPrompt != nil
    }
    
    private var hasChanges: Bool {
        guard let original = originalPrompt else {
            return !name.isEmpty || !description.isEmpty || !systemPrompt.isEmpty
        }
        return name != original.name || 
               description != original.description || 
               systemPrompt != original.systemPrompt
    }
    
    private var isValid: Bool {
        !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !systemPrompt.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    init(prompt: PromptTemplate?, onSave: @escaping (PromptTemplate) -> Void) {
        self.originalPrompt = prompt
        self.onSave = onSave
        
        if let prompt = prompt {
            _name = State(initialValue: prompt.name)
            _description = State(initialValue: prompt.description)
            _systemPrompt = State(initialValue: prompt.systemPrompt)
        }
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section("Basic Information") {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Name")
                            .font(.headline)
                        TextField("Enter prompt name", text: $name)
                            .textFieldStyle(.roundedBorder)
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Description")
                            .font(.headline)
                        TextField("Describe what this prompt does", text: $description)
                            .textFieldStyle(.roundedBorder)
                    }
                }
                
                Section("System Prompt") {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("Prompt Content")
                                .font(.headline)
                            
                            Spacer()
                            
                            Text("\(systemPrompt.count) characters")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        TextEditor(text: $systemPrompt)
                            .font(.system(.body, design: .monospaced))
                            .frame(minHeight: 200)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                            )
                        
                        Text("This prompt will guide the AI's behavior when enhancing transcriptions. Be specific about what you want the AI to do.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Section("Tips") {
                    VStack(alignment: .leading, spacing: 8) {
                        PromptTipView(
                            icon: "lightbulb",
                            title: "Be Specific",
                            description: "Clearly describe what the AI should do with the transcribed text."
                        )
                        
                        PromptTipView(
                            icon: "arrow.triangle.2.circlepath",
                            title: "Use Context",
                            description: "The AI will have access to previous statements from the session for context."
                        )
                        
                        PromptTipView(
                            icon: "text.quote",
                            title: "Output Format",
                            description: "Always end with 'Return only the enhanced dictation, no explanations.'"
                        )
                    }
                }
            }
            .navigationTitle(isEditing ? "Edit Prompt" : "New Prompt")
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        if hasChanges {
                            showingDiscardAlert = true
                        } else {
                            dismiss()
                        }
                    }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button(isEditing ? "Save" : "Create") {
                        savePrompt()
                    }
                    .disabled(!isValid)
                    .buttonStyle(.borderedProminent)
                }
            }
        }
        .alert("Discard Changes?", isPresented: $showingDiscardAlert) {
            Button("Discard", role: .destructive) {
                dismiss()
            }
            Button("Keep Editing", role: .cancel) { }
        } message: {
            Text("You have unsaved changes. Are you sure you want to discard them?")
        }
        .frame(minWidth: 500, minHeight: 600)
        .frame(idealWidth: 700, idealHeight: 700)
    }
    
    private func savePrompt() {
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedDescription = description.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedSystemPrompt = systemPrompt.trimmingCharacters(in: .whitespacesAndNewlines)

        let prompt: PromptTemplate
        if let original = originalPrompt {
            // Update existing prompt while preserving ID and creation date
            prompt = PromptTemplate(
                id: original.id,
                name: trimmedName,
                description: trimmedDescription,
                systemPrompt: trimmedSystemPrompt,
                isBuiltIn: original.isBuiltIn,
                createdAt: original.createdAt,
                updatedAt: Date()
            )
        } else {
            // Create new prompt
            prompt = PromptTemplate(
                name: trimmedName,
                description: trimmedDescription,
                systemPrompt: trimmedSystemPrompt
            )
        }

        onSave(prompt)
        dismiss()
    }
}

struct PromptTipView: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.accentColor)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

#Preview {
    PromptEditorView(prompt: nil) { _ in }
}
