#!/usr/bin/env swift

import Foundation
import AVFoundation

// Test the exact canUseMode logic from the app

print("🧪 Testing canUseMode logic...")

// Mock values based on app's actual logic
class MockViewModel {
    var canRecord: Bool = false
    
    init() {
        // Simulate the app's canRecord calculation
        let microphoneGranted = AVCaptureDevice.authorizationStatus(for: .audio) == .authorized
        let isRecording = false // Assume not recording initially
        
        canRecord = microphoneGranted && !isRecording
        print("📱 MockViewModel.canRecord = \(canRecord) (mic: \(microphoneGranted), recording: \(isRecording))")
    }
}

class MockAppState {
    var transcriptionHistory: [String] = [] // Empty initially
}

// Test canUseMode for different button types
func testCanUseMode(requiresHistory: Bool, viewModel: MockViewModel, appState: MockAppState) -> Bool {
    let hasPermissions = viewModel.canRecord
    let hasHistory = !requiresHistory || !appState.transcriptionHistory.isEmpty
    let result = hasPermissions && hasHistory
    
    print("🔍 canUseMode test:")
    print("  - hasPermissions: \(hasPermissions)")
    print("  - requiresHistory: \(requiresHistory)")
    print("  - hasHistory: \(hasHistory)")
    print("  - result: \(result)")
    
    return result
}

let mockViewModel = MockViewModel()
let mockAppState = MockAppState()

print("\n🔘 Testing Toggle Recording button (no history required):")
let toggleCanUse = testCanUseMode(requiresHistory: false, viewModel: mockViewModel, appState: mockAppState)

print("\n🔘 Testing Edit button (history required):")
let editCanUse = testCanUseMode(requiresHistory: true, viewModel: mockViewModel, appState: mockAppState)

print("\n📊 RESULTS:")
print("✅ Toggle button can be used: \(toggleCanUse)")
print("✅ Edit button can be used: \(editCanUse)")

if !toggleCanUse {
    print("❌ PROBLEM: Basic buttons are disabled!")
    if !mockViewModel.canRecord {
        print("❌ Issue: canRecord is false")
    }
} else {
    print("✅ Basic buttons should work - issue is elsewhere")
}