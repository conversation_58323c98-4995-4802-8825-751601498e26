#!/usr/bin/env swift

import Foundation

// Simple standalone test for the LLM API call
print("🧪 Testing LLM API call for F6 edit functionality...")

// Mock the LLM classes we need
import SwiftOpenAI

class TestLLMRepository {
    private var service: OpenAIService?
    
    init(apiKey: String) {
        guard !apiKey.isEmpty else { return }
        service = OpenAIServiceFactory.service(apiKey: apiKey)
    }
    
    func editText(_ text: String, instruction: String, context: String) async throws -> String {
        guard let service = service else {
            throw TestLLMError.noAPIKey
        }
        
        let prompt = """
        You are a writing assistant that edits text with contextual awareness.
        Given previous statements from the current session and an edit instruction, modify the text by:
        - applying the requested changes
        - using context to resolve any ambiguities in the instruction
        - preserving the user's original tone and intent
        - ensuring the edited text flows naturally with the previous context
        Return only the edited text, no explanations.
        
        Previous context from this session:
        \(context)
        
        Apply the edit instruction to this text: "\(text)"
        Based on this instruction: "\(instruction)"
        
        Return only the modified text:
        """
        
        let parameters = ChatCompletionParameters(
            messages: [
                .init(role: .user, content: .text(prompt))
            ],
            model: .gpt4o,
            maxTokens: 500,
            temperature: 0.3
        )
        
        print("🤖 LLM: Sending edit request to OpenAI...")
        print("🤖 LLM: Model: \(parameters.model)")
        print("🤖 LLM: Prompt length: \(prompt.count) characters")
        
        do {
            let response = try await service.startChat(parameters: parameters)
            print("🤖 LLM: Received response from OpenAI")
            
            guard let content = response.choices.first?.message.content else {
                print("❌ LLM: No content in response")
                throw TestLLMError.noResponse
            }
            
            print("🤖 LLM: Response content: '\(content)'")
            let cleanedResponse = cleanResponse(content)
            print("🤖 LLM: Cleaned response: '\(cleanedResponse)'")
            
            return cleanedResponse
        } catch {
            print("❌ LLM: API error occurred: \(error)")
            print("❌ LLM: Error type: \(type(of: error))")
            print("❌ LLM: Error description: \(error.localizedDescription)")
            throw TestLLMError.apiError(error.localizedDescription)
        }
    }
    
    private func cleanResponse(_ content: String) -> String {
        var cleaned = content.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Remove quotes if the response is wrapped in them
        if cleaned.hasPrefix("\"") && cleaned.hasSuffix("\"") {
            cleaned = String(cleaned.dropFirst().dropLast())
        }
        
        return cleaned
    }
}

enum TestLLMError: LocalizedError {
    case noAPIKey
    case noResponse
    case apiError(String)
    
    var errorDescription: String? {
        switch self {
        case .noAPIKey:
            return "OpenAI API key not configured"
        case .noResponse:
            return "No response from OpenAI API"
        case .apiError(let message):
            return "OpenAI API error: \(message)"
        }
    }
}

// Test the exact scenario that's failing
async func testEditAPI() {
    let apiKey = "********************************************************************************************************************************************************************"
    let llmRepository = TestLLMRepository(apiKey: apiKey)
    
    let originalText = "Test one, two, three."
    let editInstruction = "Remove three."
    let context = """
    Previous statements (chronological order):
    - "Test one, two, three."
    """
    
    print("🧪 Original text: '\(originalText)'")
    print("🧪 Edit instruction: '\(editInstruction)'")
    print("🧪 Context: '\(context)'")
    
    do {
        let result = try await llmRepository.editText(originalText, instruction: editInstruction, context: context)
        print("✅ LLM API call succeeded!")
        print("✅ Result: '\(result)'")
        
    } catch {
        print("❌ LLM API call failed: \(error)")
        print("❌ Error type: \(type(of: error))")
        print("❌ Error description: \(error.localizedDescription)")
    }
}

// Run the test
await testEditAPI()
print("🧪 Test completed")
